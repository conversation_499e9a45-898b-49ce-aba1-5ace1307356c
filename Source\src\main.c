/*!
    \file  main.c
    \brief led spark with systick
*/
#ifndef  __GOL_ALGORITHM_C__
#define  __GOL_ALGORITHM_C__
#include "appmain.h"
#include "nav_includes.h"
#include "frame_analysis.h"
#include "gdtypedefine.h"

uint32_t g_bmp280_measTime;
uint8_t g_usb_ready;
char g_logFileName[256] = {0};
LogBufTypeDef g_LogBuf;
LogBufTypeDef* g_pLogBuf = &g_LogBuf;
int g_networkState = -1;
uint8_t fisrtTimeGNSSTimeSync = 1;
uint8_t fpga_syn = 0;//fpga同步
int gcan0_rx_syn = 0;//can0 synchronization
int g_gpsWeek;							//GPS周内秒		周
double g_gpsSecond;						//GPS周内秒		秒
unsigned short gfpgadata[1024];
uint32_t g_CAN_Timeout_Start_flag = 0;
uint32_t g_CAN_Timeout_Cnt = 0;
uint8_t g_CAN_Count_Last = 0;
uint8_t g_KF_OutData_Rx_Flag;

extern u8 g_Uart6_Rx_Finish;//用于串口升级

//unsigned int gprotocol_send_baudrate = BAUD_RATE_921600;	//BAUD_RATE_115200, BAUD_RATE_460800, BAUD_RATE_921600
float calcGPRMC_TRA(char *pchar);
void wheel_is_running(void);
/*!
    \brief      check whether the TRNG module is ready
    \param[in]  none
    \param[out] none
    \retval     ErrStatus: SUCCESS or ERROR
*/
ErrStatus trng_ready_check(void)
{
	uint32_t timeout = 0;
	FlagStatus trng_flag = RESET;
	ErrStatus reval = SUCCESS;

	/* check wherther the random data is valid */
	do{
		timeout++;
		trng_flag = trng_flag_get(TRNG_FLAG_DRDY);
	}while((RESET == trng_flag) &&(0xFFFF > timeout));

	if(RESET == trng_flag)
	{
		/* ready check timeout */
		trng_flag = trng_flag_get(TRNG_FLAG_CECS);
		trng_flag = trng_flag_get(TRNG_FLAG_SECS);
		reval = ERROR;
	}

	/* return check status */
	return reval;
}

/*!
    \brief      configure TRNG module
    \param[in]  none
    \param[out] none
    \retval     ErrStatus: SUCCESS or ERROR
*/
ErrStatus trng_configuration(void)
{
	ErrStatus reval = SUCCESS;

	/* TRNG module clock enable */
	rcu_periph_clock_enable(RCU_TRNG);

	/* TRNG registers reset */
	trng_deinit();
	trng_enable();
	/* check TRNG work status */
	reval = trng_ready_check();

	return reval;
}

#if 0
char gmsgbuf[8];
unsigned short gmsgbuf16[4];
union {
	unsigned short bd[4];
	float fv;
	double dv;
	unsigned int iv;
} m16_uMemory;
float get_16bit_D32(unsigned short *msgbuff)
{
    m16_uMemory.bd[0] = msgbuff[0];
    m16_uMemory.bd[1] = msgbuff[1];
    return m16_uMemory.fv;
}
#endif


char gmsgbuf[8];
unsigned short gmsgbuf16[4];
union {
	unsigned short bd[4];
	float fv;
	double dv;
	unsigned int iv;
} m16_uMemory;
double get_16bit_D64(unsigned short *msgbuff)
{
	#if 1
    m16_uMemory.bd[0] = msgbuff[0];
    m16_uMemory.bd[1] = msgbuff[1];
    m16_uMemory.bd[2] = msgbuff[2];
    m16_uMemory.bd[3] = msgbuff[3];
	#else
    m16_uMemory.bd[0] = msgbuff[3];
    m16_uMemory.bd[1] = msgbuff[2];
    m16_uMemory.bd[2] = msgbuff[1];
    m16_uMemory.bd[3] = msgbuff[0];
	#endif
    return m16_uMemory.dv;
}
float get_16bit_D32(unsigned short *msgbuff)
{
    m16_uMemory.bd[0] = msgbuff[0];
    m16_uMemory.bd[1] = msgbuff[1];
    return m16_uMemory.fv;
}

unsigned int  get_16bit_Int32(unsigned short *msgbuff)
{
    m16_uMemory.bd[0] = msgbuff[0];
    m16_uMemory.bd[1] = msgbuff[1];
    return m16_uMemory.iv;
}



void gd_eval_com_init(uint32_t com,uint32_t baudval);
void gd_eval_com_init6(uint32_t com,uint32_t baudval);

void INS_Init(void)
{
	delay_init(200);
	initializationdriversettings();
	gpio_mode_set(PWM_IO_PORT, GPIO_MODE_OUTPUT, GPIO_PUPD_PULLDOWN,PWM_IO_PIN);
	gpio_output_options_set(PWM_IO_PORT, GPIO_OTYPE_PP, GPIO_OSPEED_50MHZ,PWM_IO_PIN);
	bsp_gpio_init();
	LED_WHEEL_OFF();//启动让轮速灯灭
	bsp_tim_init();
	InitFlashAddr(0);		//bill -2023-06-25 cancel//旧的flash存储初始化，删除 2024-12-11 zhangjianzhou
	//InitFlashAddr(APP_SETTING_FLASH_OFFSET);
	//SetDefaultProductInfo();	//bill -2023-06-25 cancel --- reference comm_store_init()

	UM982_PowerON();				//开启GNSS电源
	Z_AXIS_5V_PowerON();			//开启光纤陀螺电源
	MEMS_3V3_PowerON();				//开启IMU电源
	ARM1_PowerON();					//开启ARM1电源

	exmc_asynchronous_sram_init();
	Uart_TxInit(UART_TXPORT_COMPLEX_8,UART_BAUDRATE_19200BPS,UART_PARITY_NONE,UART_STOPBIT_ONE,UART_RS232,UART_ENABLE);
	Uart_RxInit(UART_RXPORT_COMPLEX_8,UART_BAUDRATE_19200BPS,UART_PARITY_NONE,UART_STOPBIT_ONE,UART_RS232,UART_ENABLE);
	//bsp_systick_init01(NULL);
	mInitCH378Host();

	CAN1_STB_High();
	CAN2_STB_High();
	CAN1_Enable();
	CAN2_Enable();

	bsp_rtc_init(pRTC);
	bsp_can_init(&hCAN0);
	bsp_can_init(&hCAN1);
	trng_configuration();
//	bsp_fwdgt_init();

	TCPServer_Init();
	bsp_exti_init();

	g_bmp280_measTime = bmp280_init();

	memset(&hINSData,0,sizeof(hINSData));
	memset(&gdriverdatalist,0,sizeof(gdriverdatalist));




	//uart4 initialization...
	nvic_irq_enable(UART4_IRQn, 0, 0);
	/* configure EVAL_COM4 */
    gd_eval_com_init(UART4, stSetPara.Setbaud*100);
    /* enable USART0 receive interrupt */
    usart_interrupt_enable(UART4, USART_INT_RBNE);


	//uart6 initialization...
	nvic_irq_enable(UART6_IRQn, 0, 0);
	/* configure EVAL_COM4 */
    gd_eval_com_init6(UART6, gprotocol_send_baudrate6);
    /* enable USART0 receive interrupt */
    usart_interrupt_enable(UART6, USART_INT_RBNE);


    /* enable USART0 transmit interrupt */
    //usart_interrupt_enable(UART4, USART_INT_TBE);

	comm_store_init();//旧的flash存储初始化，删除 2024-12-11 zhangjianzhou
	mDelaymS( 100 );
	if(RTC_BKP5 == CMD_BOOT) {
        RTC_BKP5 = 0;
        comm_send_end_frame(CMD_SET_FM_UPDATE);
    }
}

void GetChipID(void)
{
	hSetting.ChipID[0] = *(( volatile uint32_t * )0x1FFF7A10);
	hSetting.ChipID[1] = *(( volatile uint32_t * )0x1FFF7A14);
	hSetting.ChipID[2] = *(( volatile uint32_t * )0x1FFF7A18);
	hDefaultSetting.ChipID[0] = hSetting.ChipID[0];
	hDefaultSetting.ChipID[1] = hSetting.ChipID[1];
	hDefaultSetting.ChipID[2] = hSetting.ChipID[2];
}

void SetDefaultProductInfo(void)
{
	//InitFlashAddr(APP_SETTING_FLASH_OFFSET);	//bill - 2023-06-25 insert
	ReadFlash((uint8_t*)&hSetting,sizeof(AppSettingTypeDef));
	GetChipID();

	hSetting.ProductID				= PRODUCT_ID;
	hSetting.DeviceID				= DEVICE_ID;

	//strcpy(hSetting.ARM2_FW_Ver, "arm2-v0.0.1");
	//WriteFlash((uint8_t*)&hSetting,sizeof(AppSettingTypeDef));

	if(hSetting.firstProgram != FIRST_PROGRAM_BYTE)
	{
		hSetting.firstProgram = FIRST_PROGRAM_BYTE;
		//InitFlashAddr(APP_SETTING_FLASH_OFFSET);	//bill - 2023-06-25 insert
		WriteFlash((uint8_t*)&hSetting,sizeof(AppSettingTypeDef));
//		Sys_Soft_Reset();
	}

	hSetting.imuAxis = 0;
	hSetting.gInsSysStatus = INS_STATUS_INIT;
	hSetting.datamode = INS_DATA_MODE_0;

	//默认设置

	hDefaultSetting.serialFrameSetting[index_RS422].baudrate	= cfg_baud_115200;
	hDefaultSetting.serialFrameSetting[index_RS422].frameType	= cfg_format_GIPOT;
	hDefaultSetting.serialFrameSetting[index_RS422].freq		= cfg_freq_100Hz;

	hDefaultSetting.serialFrameSetting[index_RS232A].baudrate	= cfg_baud_115200;
	hDefaultSetting.serialFrameSetting[index_RS232A].frameType	= cfg_format_GPGGA;
	hDefaultSetting.serialFrameSetting[index_RS232A].freq		= cfg_freq_1Hz;

	hDefaultSetting.serialFrameSetting[index_RS232B].baudrate	= cfg_baud_9600;
	hDefaultSetting.serialFrameSetting[index_RS232B].frameType	= cfg_format_GPRMC;
	hDefaultSetting.serialFrameSetting[index_RS232B].freq		= cfg_freq_1Hz;
}



int checkUSBReady(void)
{
	UINT8  status;
	int i = 0;
	for( i = 0; i < 10; i ++ )
	{
		status = CH378DiskReady( );		/* 初始化磁盘并测试磁盘是否就绪 */
		if( status == ERR_SUCCESS )
		{
//			CH378HardwareReset();
			return 1;					/* 准备好 */
		}
		else if( status == ERR_DISK_DISCON )
		{
			return 0;					/* 检测到断开,重新检测并计时 */
		}
		if( CH378GetDiskStatus( ) >= DEF_DISK_MOUNTED && i >= 5 )
		{
			return 0;					/* 有的U盘总是返回未准备好,不过可以忽略,只要其建立连接MOUNTED且尝试5*50mS */
		}
	}
	return 0;
}

//int queryDiskCapacity(void)
//{
//	UINT8 status;
//	status = CH378DiskCapacity(cap);
//}

void loggingLogFile(void* arg)
{
	LogBufTypeDef* pBuf = (LogBufTypeDef*)arg;

	unsigned char logfileName[128] = {0};
	memset(logfileName,0,128);
	generateCSVLogFileName((char*)logfileName);
	writeCSVLog(logfileName,pBuf);
}

/* retarget the C library printf function to the USART */
int fputc(int ch, FILE *f)
{
	usart_data_transmit(USART0, (uint8_t)ch);
	while(RESET == usart_flag_get(USART0, USART_FLAG_TC));
	return ch;
}

//读取FPGA数据
void StartNavigation(void)
{
	//通知ARM1进行卡尔曼滤波解算
	ARM2_OUTPUT_ARM1_High();
	delay_us(5);
	ARM2_OUTPUT_ARM1_Low();

}

void StopNavigation(void)
{

}

void LEDIndicator(uint8_t state)
{
	switch(state)
	{
		case 0:
			break;
		case LED_STATE_WHEEL_OK_INIT_OK:	//1
			LED_STATE_ON();
			LED_WHEEL_ON();
			break;
		case LED_STATE_WHEEL_OK_INIT_ERR:	//2
			LED_STATE_OFF();
			LED_WHEEL_ON();
			break;
		case LED_STATE_WHEEL_ERR_INIT_OK:	//3
			LED_STATE_ON();
			LED_WHEEL_OFF();
			break;
		case LED_STATE_WHEEL_ERR_INIT_ERR:	//4
			LED_STATE_OFF();
			LED_WHEEL_OFF();
			break;
		default:
			break;
	}
}

/*!
    \brief      configure COM port
    \param[in]  COM: COM on the board
      \arg        EVAL_COM0: COM on the board
    \param[out] none
    \retval     none
*/
void gd_eval_com_init(uint32_t com,uint32_t baudval)
{
    /* enable GPIO clock */
    //uint32_t COM_ID = 0;
    //if(EVAL_COM0 == com)
    //{
    //    COM_ID = 0U;
    //}

    //rcu_periph_clock_enable( EVAL_COM0_GPIO_CLK);	//RCU_GPIOA
    rcu_periph_clock_enable( RCU_GPIOC);	//
    rcu_periph_clock_enable( RCU_GPIOD);	//

    /* enable USART clock */
    //rcu_periph_clock_enable(COM_CLK[COM_ID]);	//RCU_USART0
    rcu_periph_clock_enable(RCU_UART4);	//RCU_USART0

    /* connect port to USARTx_Tx */
    gpio_af_set(GPIOC, GPIO_AF_8, GPIO_PIN_12);

    /* connect port to USARTx_Rx */
    gpio_af_set(GPIOD, GPIO_AF_8, GPIO_PIN_2);

    /* configure USART Tx as alternate function push-pull */
    gpio_mode_set(GPIOC, GPIO_MODE_AF, GPIO_PUPD_PULLUP,GPIO_PIN_12);
    gpio_output_options_set(GPIOC, GPIO_OTYPE_PP, GPIO_OSPEED_50MHZ,GPIO_PIN_12);

    /* configure USART Rx as alternate function push-pull */
    gpio_mode_set(GPIOD, GPIO_MODE_AF, GPIO_PUPD_PULLUP,GPIO_PIN_2);
    gpio_output_options_set(GPIOD, GPIO_OTYPE_PP, GPIO_OSPEED_50MHZ,GPIO_PIN_2);

    /* USART configure */
    usart_deinit(com);
    usart_baudrate_set(com, baudval);
    usart_receive_config(com, USART_RECEIVE_ENABLE);
    usart_transmit_config(com, USART_TRANSMIT_ENABLE);
    usart_enable(com);
}

/*!
    \brief      configure COM port
    \param[in]  COM: COM on the board
      \arg        EVAL_COM0: COM on the board
    \param[out] none
    \retval     none
*/
void gd_eval_com_init6(uint32_t com,uint32_t baudval)
{
    /* enable GPIO clock */
    rcu_periph_clock_enable( RCU_GPIOF);

    /* enable USART clock */
    rcu_periph_clock_enable(RCU_UART6);

    /* connect port to USARTx_Tx */
    gpio_af_set(GPIOF, GPIO_AF_8, GPIO_PIN_7);

    /* connect port to USARTx_Rx */
    gpio_af_set(GPIOF, GPIO_AF_8, GPIO_PIN_6);

    /* configure USART Tx as alternate function push-pull with strong pull-up */
    gpio_mode_set(GPIOF, GPIO_MODE_AF, GPIO_PUPD_PULLUP, GPIO_PIN_7);
    gpio_output_options_set(GPIOF, GPIO_OTYPE_PP, GPIO_OSPEED_50MHZ, GPIO_PIN_7);

    /* configure USART Rx as alternate function with strong pull-up to prevent floating */
    gpio_mode_set(GPIOF, GPIO_MODE_AF, GPIO_PUPD_PULLUP, GPIO_PIN_6);
    gpio_output_options_set(GPIOF, GPIO_OTYPE_PP, GPIO_OSPEED_50MHZ, GPIO_PIN_6);

    /* USART configure */
    usart_deinit(com);
    usart_baudrate_set(com, baudval);

    /* 配置UART参数以提高抗干扰能力 */
    usart_word_length_set(com, USART_WL_8BIT);
    usart_stop_bit_set(com, USART_STB_1BIT);
    usart_parity_config(com, USART_PM_NONE);
    usart_hardware_flow_rts_config(com, USART_RTS_DISABLE);
    usart_hardware_flow_cts_config(com, USART_CTS_DISABLE);

    usart_receive_config(com, USART_RECEIVE_ENABLE);
    usart_transmit_config(com, USART_TRANSMIT_ENABLE);
    usart_enable(com);

    /* 清除可能的错误标志 */
    usart_flag_clear(com, USART_FLAG_CTS);
    usart_flag_clear(com, USART_FLAG_LBD);
    usart_flag_clear(com, USART_FLAG_TBE);
    usart_flag_clear(com, USART_FLAG_TC);
    usart_flag_clear(com, USART_FLAG_RBNE);
}


int gberror = 0;
unsigned short gpsssecond0 = 0;
unsigned short gpsssecond1 = 0;
#define OUT_GETFPGADATA_DEBUG_INFO	0

	//********转台数据标定的数值***标度因素20250528****
	//FOG50: 5号（SN:202303090001）：301350   6号（SN:202303090008）：294790
 //FOG60: 4号（SN:202402010004）：577002		17号（SN:202402010003）：576260  10号（SN:202402010001）：577014
 //FOG60: 6号（SN:202303090001）：575865		


#define FOG_GYRO_FACTOR 299820

void get_fpgadata(void)
{
	char debugbuff[1024]={0};
	char GPRMC_TRA[8];
#if !OUT_GETFPGADATA_DEBUG_INFO
	int	i;
	int fpgasenddatalen = 0;
	fpgasenddatalen = *(unsigned short*)(0x60000000 + (0 << 1));
	if (fpgasenddatalen >= 1024)	return;
	for (i = 0; i < fpgasenddatalen; i++) {			//从FPGA接受20 Word, 并转换为40个字符
		gfpgadata[i] = *(unsigned short*)(0x60000000 + (i << 1));	//从FPGA读取16bit数据
	}
	gpagedata = *(fpgadata_t*)&gfpgadata;

	memset(&hGPSData, 0, sizeof(hGPSData));

	if (gpsssecond0 == gpagedata.hGPSData_gpssecond0 && gpsssecond1 == gpagedata.hGPSData_gpssecond1) {
		gpsssecond0 = gpagedata.hGPSData_gpssecond0;
		gpsssecond1 = gpagedata.hGPSData_gpssecond1;
		gberror = 1;
	}
	gpsssecond0 = gpagedata.hGPSData_gpssecond0;
	gpsssecond1 = gpagedata.hGPSData_gpssecond1;

	if (gberror) {
		//gpagedata.hGPSData_gpssecond0 = 3691;
		//gpagedata.hGPSData_gpssecond1 = 0;
		//gpagedata.Dtemp_data = 0;
		//gpagedata.Utemp_data = 90;
	}

  //gyc20240725增加***************************************

        memcpy(&combineData.ppsDelay ,        &gfpgadata[0x32],4);
        memcpy(&combineData.factor_G ,        &gfpgadata[0x49],4);
        memcpy(&combineData.factor_pos ,						&gfpgadata[0x4B],4);
        memcpy(&combineData.factor_time,						&gfpgadata[0x4D],4);
        memcpy(&combineData.factor_V ,        &gfpgadata[0x4F],4);
        memcpy(&combineData.factor_H ,        &gfpgadata[0x51],4);
        memcpy(&combineData.factor_N ,        &gfpgadata[0x53],4);
        memcpy(&combineData.factor_E ,        &gfpgadata[0x55],4);

	//gyc20240725增加***************************************
	//imu
	combineData.scha634Info.acc_x = (gpagedata.accX_data / 4905.0);
	combineData.scha634Info.acc_y = (gpagedata.accY_data / 4905.0);
	combineData.scha634Info.acc_z = (gpagedata.accZ_data / 4905.0);
	combineData.scha634Info.gyro_x = (gpagedata.rateX_data / 80.0);
	combineData.scha634Info.gyro_y = (gpagedata.rateY_data / 80.0);
	combineData.scha634Info.gyro_z = (gpagedata.rateZ_data / 80.0);
	combineData.scha634Info.temp_due = (25 + gpagedata.Dtemp_data / 30.0);
	combineData.scha634Info.temp_uno = (25 + gpagedata.Utemp_data / 30.0);

	hINSCANData.data_stream.accelX = (combineData.scha634Info.acc_x + 4) / 0.0001220703125;
	hINSCANData.data_stream.accelY = (combineData.scha634Info.acc_y + 4) / 0.0001220703125;
	hINSCANData.data_stream.accelZ = (combineData.scha634Info.acc_z + 4) / 0.0001220703125;
	hINSCANData.data_stream.gyroX = (combineData.scha634Info.gyro_x + 250) / 0.0076293;
	hINSCANData.data_stream.gyroY = (combineData.scha634Info.gyro_y + 250) / 0.0076293;
	hINSCANData.data_stream.gyroZ = (combineData.scha634Info.gyro_z + 250) / 0.0076293;

	//gnss
	combineData.gnssInfo.gpsweek = gpagedata.hGPSData_gpsweek;
	hGPSData.gpsweek = combineData.gnssInfo.gpsweek;
	combineData.gnssInfo.gpssecond = get_16bit_Int32((unsigned short *)&gpagedata.hGPSData_gpssecond0);
	//combineData.gnssInfo.gpssecond = combineData.gnssInfo.gpssecond+((unsigned char)(gpagedata.NUM_CLK >>8))*5;
	if(fabs(combineData.gnssInfo.gpssecond)>(604800*1000) || combineData.gnssInfo.gpssecond<0)
	{
		memset(debugbuff,0,sizeof(debugbuff));
		sprintf(debugbuff,"Error gpssecond, gpssecond0=0x%04X,gpssecond1=0x%04X,NUM_CLK=0x%04X,gpssecond=%d\r\n",\
			gpagedata.hGPSData_gpssecond0,gpagedata.hGPSData_gpssecond1,gpagedata.NUM_CLK>>8,combineData.gnssInfo.gpssecond);
		uart4sendmsg(debugbuff, strlen(debugbuff));
	}
	hGPSData.gpssecond = combineData.gnssInfo.gpssecond;

	combineData.gnssInfo.headingStatus = (gpagedata.hGPSData_headingStatus >> 8);
	combineData.gnssInfo.FpgaGnssUpdate = (gpagedata.hGPSData_headingStatus >> 0) & 0xff;

	//hGPSData.headingStatus = combineData.gnssInfo.headingStatus;
	combineData.gnssInfo.rtkStatus = (gpagedata.hGPSData_rtkStatus >> 8);
	hGPSData.rtkStatus = combineData.gnssInfo.rtkStatus;

	combineData.gnssInfo.vn = get_16bit_D32((unsigned short *)&gpagedata.hGPSData_vn0);
	if(fabs(combineData.gnssInfo.vn)>30)
	{
		memset(debugbuff,0,sizeof(debugbuff));
		sprintf(debugbuff,"Error gnssInfo.vn, hGPSData_vn0=0x%04X,hGPSData_vn1=0x%04X,vn=%f\r\n",\
			gpagedata.hGPSData_vn0,gpagedata.hGPSData_vn1,combineData.gnssInfo.vn);
		uart4sendmsg(debugbuff, strlen(debugbuff));
	}
	hGPSData.vn = combineData.gnssInfo.vn;

	combineData.gnssInfo.ve = get_16bit_D32((unsigned short *)&gpagedata.hGPSData_ve0);
	if(fabs(combineData.gnssInfo.ve)>30)
	{
		memset(debugbuff,0,sizeof(debugbuff));
		sprintf(debugbuff,"Error gnssInfo.ve, hGPSData_ve0=0x%04X,hGPSData_ve1=0x%04X,ve=%f\r\n",\
			gpagedata.hGPSData_ve0,gpagedata.hGPSData_ve1,combineData.gnssInfo.ve);
		uart4sendmsg(debugbuff, strlen(debugbuff));
	}
	hGPSData.ve = combineData.gnssInfo.ve;

	combineData.gnssInfo.vu = get_16bit_D32((unsigned short *)&gpagedata.hGPSData_vu0);
	if(fabs(combineData.gnssInfo.vu)>30)
	{
		memset(debugbuff,0,sizeof(debugbuff));
		sprintf(debugbuff,"Error gnssInfo.vu, hGPSData_vu0=0x%04X,hGPSData_vu1=0x%04X,vu=%f\r\n",\
			gpagedata.hGPSData_vu0,gpagedata.hGPSData_vu1,combineData.gnssInfo.vu);
		uart4sendmsg(debugbuff, strlen(debugbuff));
	}
	hGPSData.vu = combineData.gnssInfo.vu;

	combineData.gnssInfo.Heading = get_16bit_D32((unsigned short *)&gpagedata.hGPSData_Heading0);
	hGPSData.Heading = combineData.gnssInfo.Heading;
	combineData.gnssInfo.Pitch = get_16bit_D32((unsigned short *)&gpagedata.hGPSData_Pitch0);
	hGPSData.Pitch = combineData.gnssInfo.Pitch;
	combineData.gnssInfo.Roll = get_16bit_D32((unsigned short *)&gpagedata.hGPSData_Roll0);
	hGPSData.Roll = combineData.gnssInfo.Roll;

	combineData.gnssInfo.Lat = get_16bit_D64((unsigned short *)&gpagedata.hGPSData_Lat0);
	if(fabs(combineData.gnssInfo.Lat)>180)
	{
		memset(debugbuff,0,sizeof(debugbuff));
		sprintf(debugbuff,"Error gnssInfo.Lat, hGPSData_Lat0=0x%04X,hGPSData_Lat1=0x%04X,Lat=%f\r\n",\
			gpagedata.hGPSData_Lat0,gpagedata.hGPSData_Lat1,combineData.gnssInfo.Lat);
		uart4sendmsg(debugbuff, strlen(debugbuff));
	}
	hGPSData.Lat = combineData.gnssInfo.Lat;

	combineData.gnssInfo.Lon = get_16bit_D64((unsigned short *)&gpagedata.hGPSData_Lon0);
	if(fabs(combineData.gnssInfo.Lon)>180)
	{
		memset(debugbuff,0,sizeof(debugbuff));
		sprintf(debugbuff,"Error gnssInfo.Lon, hGPSData_Lon0=0x%04X,hGPSData_Lon1=0x%04X,Lon=%f\r\n",\
			gpagedata.hGPSData_Lon0,gpagedata.hGPSData_Lon1,combineData.gnssInfo.Lon);
		uart4sendmsg(debugbuff, strlen(debugbuff));
	}
	hGPSData.Lon = combineData.gnssInfo.Lon;

	combineData.gnssInfo.Altitude = get_16bit_D64((unsigned short *)&gpagedata.hGPSData_Alt0);
	if(fabs(combineData.gnssInfo.Altitude)>10000)
	{
		memset(debugbuff,0,sizeof(debugbuff));
		sprintf(debugbuff,"Error gnssInfo.Altitude, hGPSData_Alt0=0x%04X,hGPSData_Alt1=0x%04X,Altitude=%f\r\n",\
			gpagedata.hGPSData_Alt0,gpagedata.hGPSData_Alt1,combineData.gnssInfo.Altitude);
		uart4sendmsg(debugbuff, strlen(debugbuff));
	}
	hGPSData.Altitude = combineData.gnssInfo.Altitude;

	//combineData.gnssInfo.StarNum = (gpagedata.GPGGA_STAR >> 8);
	//combineData.gnssInfo.StarNum = ((gpagedata.GPGGA_STAR >> 8) - 0x30) * 10 + ((gpagedata.GPGGA_STAR & 0xff) - 0x30);
	combineData.gnssInfo.StarNum = (gpagedata.GPGGA_STAR >> 0);
	hGPSData.StarNum = combineData.gnssInfo.StarNum;
	combineData.gnssInfo.baseline =get_16bit_D32((unsigned short *)&gpagedata.HEADING_BAS0);
	hGPSData.baseline = combineData.gnssInfo.baseline;
	combineData.gnssInfo.PositioningState = (gpagedata.GPRMC_POS >> 8);
	hGPSData.PositioningState = combineData.gnssInfo.PositioningState;
	combineData.gnssInfo.LonHemisphere = (gpagedata.GPRMC_LON >> 8);
	hGPSData.LonHemisphere = combineData.gnssInfo.LonHemisphere;
	combineData.gnssInfo.LatHemisphere = (gpagedata.GPRMC_LAT >> 8);
	hGPSData.LatHemisphere = combineData.gnssInfo.LatHemisphere;


	GPRMC_TRA[0] = (gpagedata.GPRMC_TRA[2] >> 8) & 0xff;
	GPRMC_TRA[1] = (gpagedata.GPRMC_TRA[1] >> 8) & 0xff;
	GPRMC_TRA[2] = (gpagedata.GPRMC_TRA[1] >> 0) & 0xff;
	GPRMC_TRA[3] = (gpagedata.GPRMC_TRA[0] >> 8) & 0xff;
	GPRMC_TRA[4] = (gpagedata.GPRMC_TRA[0] >> 0) & 0xff;
	combineData.gnssInfo.trackTrue = calcGPRMC_TRA(GPRMC_TRA);


	//combineData.gnssInfo.trackTrue    = ins_fgpa_get_trackTrue(g_fpga_buff+index,6);        index+=6;
	//combineData.gnssInfo.gpssecond982 = get_UInt32(g_fpga_buff,index);                      index+=4;
	//combineData.ifogInfo.sensorTemp   = get_short(g_fpga_buff,index)/16.0;                  index+=2;
	//combineData.ifogInfo.gyroGrp[2]   = get_Int32(g_fpga_buff,index)/FOG_GYRO_FACTOR;       index+=4;
	//combineData.gnssInfo.velStatus    = g_fpga_buff[index+1];                               index+=2;

	//combineData.gnssInfo.trackTrue    = ins_fgpa_get_trackTrue(g_fpga_buff+index,6);        index+=6;
	combineData.gnssInfo.gpssecond982 = get_16bit_Int32((unsigned short *)&gpagedata.gpssecond9820);
//	combineData.ifogInfo.sensorTemp   = (short)gpagedata.sensorTemp / 16.0;
//	combineData.ifogInfo.gyroGrp[2]   = (int)get_16bit_D32((unsigned short *)&gpagedata.gyroGrp20) / FOG_GYRO_FACTOR;
	combineData.gnssInfo.velStatus    = (gpagedata.velStatus>> 8);

	hGPSData.trackTrue = combineData.gnssInfo.trackTrue;

	gfog0 = (int)get_16bit_Int32((unsigned short *)&gpagedata.fog_x0) / 1.0;
	combineData.ifogInfo.gyroGrp[2] = gfog0 / (FOG_GYRO_FACTOR * 1.0);

	memcpy(&hGPSData01, &hGPSData, sizeof(hGPSData01));
	if (gsystemflag & c_systemflag_rtkoff) {
		memset(&hGPSData, 0, sizeof(hGPSData));
	}




	//
	//combineData.gnssInfo.trackTrue = (gpagedata.GPRMC_TRA);
	//combineData.gnssInfo.trackTrue = (gpagedata.GPRMC_TRA);


	//combineData.gnssInfo.gpsweek = (gpagedata.VERSION);
	//combineData.gnssInfo.baseline	=1.0;//!!!!feng modify

	//combineData.canInfo.data = hINSData.CAN_Data;
	//CAN
	#if 0
//    float timestamp;					/* 时间戳, 单位:s , 精度：0.0001*/
//    float WheelSpeed_Front_Left;		/* 轮速 左前, 单位: m/s, 精度：待定*/
//    float WheelSpeed_Back_Left;		/* 轮速 左后, 单位: m/s, 精度：待定*/
//    float WheelSpeed_Front_Right;		/* 轮速 右前, 单位: m/s, 精度：待定*/
//    float WheelSpeed_Back_Right;		/* 轮速 右后, 单位: m/s, 精度：待定*/
//    float WheelSteer;					/* 方向盘, 单位: °, 	 精度：待定*/
//    float OdoPulse_1;					/* 里程计脉冲, 单位:  个数, 精度: 待定 */
//    float OdoPulse_2;					/* 里程计脉冲, 单位:  个数, 精度: 待定 */
//    uint8_t Gear;						/* 汽车档位 */
	combineData.canInfo.flag = 0;
	combineData.canInfo.counter = 0;
	combineData.canInfo.data.timestamp = 0;
	combineData.canInfo.data.WheelSpeed_Front_Left = 0;
	combineData.canInfo.data.WheelSpeed_Back_Left = 0;
	combineData.canInfo.data.WheelSpeed_Front_Right = 0;
	combineData.canInfo.data.WheelSpeed_Back_Right = 0;
	combineData.canInfo.data.WheelSteer = 0;
	combineData.canInfo.data.OdoPulse_1 = 0;
	combineData.canInfo.data.OdoPulse_2 = 0;
	combineData.canInfo.data.Gear = 0;
	#endif
#else
	int	i, index = 0;
	int fpgasenddatalen = 0;
	unsigned short fpga16 = 0;
	int loopcnt = 0;		//循环计数，为了串口显示MCU在正常执行代码
	char txbuf[200];		//临时测试变量，仅为了输出串口内容
	char tmptxt[10];
	char fpgatesttxt[200];	//临时测试变量，仅为了测试从FPGA传来的数据
	int bover = 0;
	while (1) {
		delay_ms(10);
		loopcnt++;
		if (loopcnt % 100 == 0) {	//串口表示MCU有在正常执行代码
			sprintf(txbuf, "loop count = %d.\r\n", loopcnt / 100);
			uart4sendmsg(txbuf, strlen(txbuf));
		}
		//fpga_syn = 1;
		if(fpga_syn) {	//串口显示MCU从FPGA收到的数据
			fpga_syn = 0;
			memset(fpgatesttxt, 0, sizeof(fpgatesttxt));
			fpgasenddatalen = *(unsigned short*)(0x60000000 + (0 << 1));
			if (fpgasenddatalen >= 1024)	return;
			for (i = 0; i < fpgasenddatalen; i++) {			//从FPGA接受20 Word, 并转换为40个字符
				gfpgadata[i] = *(unsigned short*)(0x60000000 + (i << 1));	//从FPGA读取16bit数据
			}
			gpagedata = *(fpgadata_t*)&gfpgadata;

			memset(fpgatesttxt, 0, sizeof(fpgatesttxt));
			for (i = 0; i < fpgasenddatalen; i++) {
				sprintf(tmptxt, "0x%04x ", gfpgadata[i]);
				strcat(fpgatesttxt, tmptxt);
				bover = 0;
				if (i % 10 == 9) {
					sprintf(txbuf, "A%04d: %s.\r\n", i - 9, fpgatesttxt);
					uart4sendmsg(txbuf, strlen(txbuf));
					memset(fpgatesttxt, 0, sizeof(fpgatesttxt));
					bover = 1;
				}
			}
			if (bover == 0) {
				sprintf(txbuf, "A%04d: %s.\r\n", (i / 10) * 10, fpgatesttxt);
				uart4sendmsg(txbuf, strlen(txbuf));
				memset(fpgatesttxt, 0, sizeof(fpgatesttxt));
			}


			printf_uart4(0, "gpagedata.              DATA_LEN  0x%04x\t%d.\r\n", gpagedata.DATA_LEN, gpagedata.DATA_LEN);
			printf_uart4(0, "gpagedata.               NUM_CLK  0x%04x\t%d.\r\n", (unsigned char)(gpagedata.NUM_CLK >> 8), (unsigned char)(gpagedata.NUM_CLK >> 8));
			printf_uart4(0, "gpagedata.            Dtemp_data  0x%04x\t%d.\t%.2f\r\n", (unsigned short)gpagedata.Dtemp_data, gpagedata.Dtemp_data, (25 + gpagedata.Dtemp_data / 30.0));
			printf_uart4(0, "gpagedata.            Utemp_data  0x%04x\t%d.\t%.2f\r\n", (unsigned short)gpagedata.Utemp_data, gpagedata.Utemp_data, (25 + gpagedata.Utemp_data / 30.0));

			printf_uart4(0, "gpagedata             .accZ_data  0x%04x\t%d.\t%.4f\r\n", (unsigned short)gpagedata.accZ_data, gpagedata.accZ_data, (gpagedata.accZ_data / 4905.0));
			printf_uart4(0, "gpagedata             .accY_data  0x%04x\t%d.\t%.4f\r\n", (unsigned short)gpagedata.accY_data, gpagedata.accY_data, (gpagedata.accY_data / 4905.0));
			printf_uart4(0, "gpagedata             .accX_data  0x%04x\t%d.\t%.4f\r\n", (unsigned short)gpagedata.accX_data, gpagedata.accX_data, (gpagedata.accX_data / 4905.0));
			printf_uart4(0, "gpagedata            .rateZ_data  0x%04x\t%d.\t%.4f\r\n", (unsigned short)gpagedata.rateZ_data, gpagedata.rateZ_data, (gpagedata.rateZ_data / 80.0));
			printf_uart4(0, "gpagedata            .rateY_data  0x%04x\t%d.\t%.4f\r\n", (unsigned short)gpagedata.rateY_data, gpagedata.rateY_data, (gpagedata.rateY_data / 80.0));
			printf_uart4(0, "gpagedata            .rateX_data  0x%04x\t%d.\t%.4f\r\n", (unsigned short)gpagedata.rateX_data, gpagedata.rateX_data, (gpagedata.rateX_data / 80.0));

			printf_uart4(0, "gpagedata      .hGPSData_gpsweek  0x%04x\t%d.\r\n", (unsigned short)gpagedata.hGPSData_gpsweek, gpagedata.hGPSData_gpsweek);
			printf_uart4(0, "gpagedata   .hGPSData_gpssecond0  0x%04x\t%d.\r\n", (unsigned short)gpagedata.hGPSData_gpssecond0, gpagedata.hGPSData_gpssecond0);
			printf_uart4(0, "gpagedata   .hGPSData_gpssecond1  0x%04x\t%d.\t%d\r\n", (unsigned short)gpagedata.hGPSData_gpssecond1, gpagedata.hGPSData_gpssecond1, (gpagedata.hGPSData_gpssecond1 << 16) + gpagedata.hGPSData_gpssecond0);

			printf_uart4(0, "gpagedata.hGPSData_headingStatus  0x%04x\t%d.\r\n", (unsigned short)(gpagedata.hGPSData_headingStatus >> 0), (gpagedata.hGPSData_headingStatus >> 0));
			printf_uart4(0, "gpagedata    .hGPSData_rtkStatus  0x%04x\t%d.\r\n", (unsigned short)(gpagedata.hGPSData_rtkStatus >> 8), (gpagedata.hGPSData_rtkStatus >> 8));

			printf_uart4(0, "gpagedata          .hGPSData_vn0  0x%04x\t%d.\r\n", (unsigned short)gpagedata.hGPSData_vn0, gpagedata.hGPSData_vn0);
			printf_uart4(0, "gpagedata          .hGPSData_vn1  0x%04x\t%d.\t%.8f\r\n", (unsigned short)gpagedata.hGPSData_vn1, gpagedata.hGPSData_vn1, get_16bit_D32((unsigned short *)&gpagedata.hGPSData_vn0));
			printf_uart4(0, "gpagedata          .hGPSData_ve0  0x%04x\t%d.\r\n", (unsigned short)gpagedata.hGPSData_ve0, gpagedata.hGPSData_ve0);
			printf_uart4(0, "gpagedata          .hGPSData_ve1  0x%04x\t%d.\t%.8f\r\n", (unsigned short)gpagedata.hGPSData_ve1, gpagedata.hGPSData_ve1, get_16bit_D32((unsigned short *)&gpagedata.hGPSData_ve0));
			printf_uart4(0, "gpagedata          .hGPSData_vu0  0x%04x\t%d.\r\n", (unsigned short)gpagedata.hGPSData_vu0, gpagedata.hGPSData_vu0);
			printf_uart4(0, "gpagedata          .hGPSData_vu1  0x%04x\t%d.\t%.8f\r\n", (unsigned short)gpagedata.hGPSData_vu1, gpagedata.hGPSData_vu1, get_16bit_D32((unsigned short *)&gpagedata.hGPSData_vu0));

			printf_uart4(0, "gpagedata     .hGPSData_Heading0  0x%04x\t%d.\r\n", (unsigned short)gpagedata.hGPSData_Heading0, gpagedata.hGPSData_Heading0);
			printf_uart4(0, "gpagedata     .hGPSData_Heading1  0x%04x\t%d.\t%.8f\r\n", (unsigned short)gpagedata.hGPSData_Heading1, gpagedata.hGPSData_Heading1, get_16bit_D32((unsigned short *)&gpagedata.hGPSData_Heading0));
			printf_uart4(0, "gpagedata       .hGPSData_Pitch0  0x%04x\t%d.\r\n", (unsigned short)gpagedata.hGPSData_Pitch0, gpagedata.hGPSData_Pitch0);
			printf_uart4(0, "gpagedata       .hGPSData_Pitch1  0x%04x\t%d.\t%.8f\r\n", (unsigned short)gpagedata.hGPSData_Pitch1, gpagedata.hGPSData_Pitch1, get_16bit_D32((unsigned short *)&gpagedata.hGPSData_Pitch0));
			printf_uart4(0, "gpagedata        .hGPSData_Roll0  0x%04x\t%d.\r\n", (unsigned short)gpagedata.hGPSData_Roll0, gpagedata.hGPSData_Roll0);
			printf_uart4(0, "gpagedata        .hGPSData_Roll1  0x%04x\t%d.\t%.8f\r\n", (unsigned short)gpagedata.hGPSData_Roll1, gpagedata.hGPSData_Roll1, get_16bit_D32((unsigned short *)&gpagedata.hGPSData_Roll0));

			//double lat=27.123456789;
			//63 39 37 DD 9A 1F 3B 40
			//40 3B 1F 9A DD 37 39 63
			#if 0
			gmsgbuf[0] = 0x63;
			gmsgbuf[1] = 0x39;
			gmsgbuf[2] = 0x37;
			gmsgbuf[3] = 0xdd;
			gmsgbuf[4] = 0x9a;
			gmsgbuf[5] = 0x1f;
			gmsgbuf[6] = 0x3b;
			gmsgbuf[7] = 0x40;
			#endif
			#if 0
			gpagedata.hGPSData_Lat0 = 0x65A2;
			gpagedata.hGPSData_Lat1 = 0xCF10;
			gpagedata.hGPSData_Lat2 = 0xB281;
			gpagedata.hGPSData_Lat3 = 0x4036;
			#endif
			printf_uart4(0, "gpagedata         .hGPSData_Lat0  0x%04x\t%d.\r\n", (unsigned short)gpagedata.hGPSData_Lat0, gpagedata.hGPSData_Lat0);
			printf_uart4(0, "gpagedata         .hGPSData_Lat1  0x%04x\t%d.\r\n", (unsigned short)gpagedata.hGPSData_Lat1, gpagedata.hGPSData_Lat1);
			printf_uart4(0, "gpagedata         .hGPSData_Lat2  0x%04x\t%d.\r\n", (unsigned short)gpagedata.hGPSData_Lat2, gpagedata.hGPSData_Lat2);
			printf_uart4(0, "gpagedata         .hGPSData_Lat3  0x%04x\t%d.\t%08lf\r\n", (unsigned short)gpagedata.hGPSData_Lat3, gpagedata.hGPSData_Lat3, get_16bit_D64((unsigned short *)&gpagedata.hGPSData_Lat0));

			printf_uart4(0, "gpagedata         .hGPSData_Lon0  0x%04x\t%d.\r\n", (unsigned short)gpagedata.hGPSData_Lon0, gpagedata.hGPSData_Lon0);
			printf_uart4(0, "gpagedata         .hGPSData_Lon1  0x%04x\t%d.\r\n", (unsigned short)gpagedata.hGPSData_Lon1, gpagedata.hGPSData_Lon1);
			printf_uart4(0, "gpagedata         .hGPSData_Lon2  0x%04x\t%d.\r\n", (unsigned short)gpagedata.hGPSData_Lon2, gpagedata.hGPSData_Lon2);
			printf_uart4(0, "gpagedata         .hGPSData_Lon3  0x%04x\t%d.\t%08lf\r\n", (unsigned short)gpagedata.hGPSData_Lon3, gpagedata.hGPSData_Lon3, get_16bit_D64((unsigned short *)&gpagedata.hGPSData_Lon0));

			printf_uart4(0, "gpagedata         .hGPSData_Alt0  0x%04x\t%d.\r\n", (unsigned short)gpagedata.hGPSData_Alt0, gpagedata.hGPSData_Alt0);
			printf_uart4(0, "gpagedata         .hGPSData_Alt1  0x%04x\t%d.\r\n", (unsigned short)gpagedata.hGPSData_Alt1, gpagedata.hGPSData_Alt1);
			printf_uart4(0, "gpagedata         .hGPSData_Alt2  0x%04x\t%d.\r\n", (unsigned short)gpagedata.hGPSData_Alt2, gpagedata.hGPSData_Alt2);
			printf_uart4(0, "gpagedata         .hGPSData_Alt3  0x%04x\t%d.\t%08lf\r\n", (unsigned short)gpagedata.hGPSData_Alt3, gpagedata.hGPSData_Alt3, get_16bit_D64((unsigned short *)&gpagedata.hGPSData_Alt0));

			printf_uart4(0, "gpagedata            .GPGGA_STAR  %c%c\t\t%d.\r\n", (gpagedata.GPGGA_STAR >> 8), (gpagedata.GPGGA_STAR >> 0));	//ascii code 2byte
			printf_uart4(0, "gpagedata           .HEADING_BAS  0x%04x\t%d.\r\n", (unsigned short)gpagedata.HEADING_BAS0, (unsigned short)gpagedata.HEADING_BAS0);
			printf_uart4(0, "gpagedata           .HEADING_BAS  0x%04x\t%d.\t%08f\r\n", (unsigned short)gpagedata.HEADING_BAS1, (unsigned short)gpagedata.HEADING_BAS1, get_16bit_D32((unsigned short *)&gpagedata.HEADING_BAS0));

			printf_uart4(0, "gpagedata             .GPRMC_POS  0x%04x\t%d.\r\n", (unsigned short)(gpagedata.GPRMC_POS >> 8), (gpagedata.GPRMC_POS >> 8));
			printf_uart4(0, "gpagedata             .GPRMC_LON  0x%04x\t%d.\r\n", (unsigned short)(gpagedata.GPRMC_LON >> 8), (gpagedata.GPRMC_LON >> 8));
			printf_uart4(0, "gpagedata             .GPRMC_LAT  0x%04x\t%d.\r\n", (unsigned short)(gpagedata.GPRMC_LAT >> 8), (gpagedata.GPRMC_LAT >> 8));
			GPRMC_TRA[0] = (gpagedata.GPRMC_TRA[2] >> 8) & 0xff;
			GPRMC_TRA[1] = (gpagedata.GPRMC_TRA[1] >> 8) & 0xff;
			GPRMC_TRA[2] = (gpagedata.GPRMC_TRA[1] >> 0) & 0xff;
			GPRMC_TRA[3] = (gpagedata.GPRMC_TRA[0] >> 8) & 0xff;
			GPRMC_TRA[4] = (gpagedata.GPRMC_TRA[0] >> 0) & 0xff;
			GPRMC_TRA[5] = '\0';
			printf_uart4(0, "gpagedata             .GPRMC_TRA  %c%c%c%c%c\t\t%.1f\t%08f\r\n",  (gpagedata.GPRMC_TRA[2] >> 8) & 0xff, (gpagedata.GPRMC_TRA[1] >> 8) & 0xff,\
																				   (gpagedata.GPRMC_TRA[1] >> 0) & 0xff, (gpagedata.GPRMC_TRA[0] >> 8) & 0xff,\
																				   (gpagedata.GPRMC_TRA[0] >> 0) & 0xff, calcGPRMC_TRA(GPRMC_TRA), calcGPRMC_TRA(GPRMC_TRA));	//ascii 3 word

			printf_uart4(0, "gpagedata          .gpssecond982  0x%04x\t%d.\t%.8f\r\n", (unsigned short)gpagedata.gpssecond9820, gpagedata.gpssecond9821, get_16bit_D32((unsigned short *)&gpagedata.gpssecond9820));
			printf_uart4(0, "gpagedata            .sensorTemp  0x%04x\t%d.\r\n", (unsigned short)(gpagedata.GPRMC_LAT >> 8), (gpagedata.GPRMC_LAT >> 8));
			printf_uart4(0, "gpagedata            .gyroGrp[2]  0x%04x\t%d.\t%.8f\r\n", (unsigned short)gpagedata.gyroGrp20, gpagedata.gyroGrp21, get_16bit_D32((unsigned short *)&gpagedata.gyroGrp20));
			printf_uart4(0, "gpagedata             .velStatus  0x%04x\t%d.\r\n", (unsigned short)(gpagedata.velStatus >> 0), (gpagedata.velStatus >> 0));
			//combineData.gnssInfo.gpssecond982 = get_16bit_D32((unsigned short *)&gpagedata.gpssecond9820);
			//combineData.ifogInfo.sensorTemp   = gpagedata.sensorTemp;
			//combineData.ifogInfo.gyroGrp[2]   = (int)get_16bit_D32((unsigned short *)&gpagedata.gyroGrp20) / FOG_GYRO_FACTOR;
			//combineData.gnssInfo.velStatus    = gpagedata.velStatus;



			//printf_uart4(0, "gpagedata      .hGPSData Heading  0x%04x\t%d.\t%.8f\r\n", (unsigned short)gpagedata.Heading0, gpagedata.Heading1, get_16bit_D32((unsigned short *)&gpagedata.Heading0));
			//printf_uart4(0, "gpagedata        .hgpsData Pitch  0x%04x\t%d.\t%.8f\r\n", (unsigned short)gpagedata.Pitch0, gpagedata.Pitch1, get_16bit_D32((unsigned short *)&gpagedata.Pitch0));

			printf_uart4(0, "gpagedata               .VERSION  0x%04x\t%d.\r\n", (unsigned short)gpagedata.VERSION, gpagedata.VERSION);
			printf_uart4(0, "packet: %d\r\n\r\n\r\n", ++index);
		}
	}
	#endif
	frame_form();
}

float calcGPRMC_TRA(char *pchar)
{
	char tmpchar;
	float multiplier = 1.0;
	int i, bfind = 0;
	float tra = 0.0;
	for (i = 0; i < 5; i++) {
		if (*(pchar + i) == '.') {
			bfind = 1;
			break;
		}
	}
	if (bfind) {
		tmpchar = *(pchar + i + 1);
		if (tmpchar < '0' || tmpchar > '9')	return	888.8;
		else tra = (tmpchar - '0') * 0.1;

		for (i-- ; i >= 0; i--) {
			tmpchar = *(pchar + i);
			if (tmpchar < '0' || tmpchar > '9')	break;
			else tra += (tmpchar - '0') * multiplier;
			multiplier *= 10.0;
		}
		return tra;
	}
	return 999.9;
}

void sys_irq_stop(void)
{
//    gd32_pin_irq_enable(FPGA_TO_ARM1_INT, PIN_IRQ_DISABLE);
//    gd32_pin_irq_enable(ARM1_TO_ARM2_IO, PIN_IRQ_DISABLE);
//    gd32_pin_irq_enable(FPGA_PPS_ARM1_INT, PIN_IRQ_DISABLE);
//    gd32_pin_irq_enable(FPGA_TX_TO_ARM1_INT, PIN_IRQ_DISABLE);
}

void sys_irq_restart(void)
{
//    gd32_pin_irq_enable(FPGA_TO_ARM1_INT, PIN_IRQ_ENABLE);
//    gd32_pin_irq_enable(ARM1_TO_ARM2_IO, PIN_IRQ_ENABLE);
//    gd32_pin_irq_enable(FPGA_PPS_ARM1_INT, PIN_IRQ_ENABLE);
//    gd32_pin_irq_enable(FPGA_TX_TO_ARM1_INT, PIN_IRQ_ENABLE);
}


#define RE_WGS84		6378137.0						/* earth semimajor axis (WGS84) (m) */
#define Rp_WGS84		((1-(FE_WGS84))*(RE_WGS84))		// 短半轴

int goutputmode = c_outputmode_normal | c_outputmode_runalg;//正常组合导航输出
//int goutputmode = c_outputmode_gdw | c_outputmode_runalg;//原始观测量输出
int g_Outmode = 0;

unsigned int gprotocol_send_baudrate =BAUD_RATE_460800;// BAUD_RATE_2000000;	//BAUD_RATE_115200, BAUD_RATE_460800, BAUD_RATE_921600, BAUD_RATE_2000000
unsigned int gprotocol_send_baudrate6 = BAUD_RATE_115200;	//BAUD_RATE_115200, BAUD_RATE_460800, BAUD_RATE_921600, BAUD_RATE_2000000, BAUD_RATE_230400
int gbilldebuguart4 = 0;	//0- normal		1- bill debug	for debug can


int main(void)
{
  g_Outmode = goutputmode;
	char versionbuf[32];
	hw_interrupt_disable();
#ifdef BOOT_LOADER
    nvic_vector_table_set(NVIC_VECTTAB_FLASH, APP_LOADED_ADDR);
    __asm("CPSIE  I");	 //open ir…q
#endif
	if (RESET != rcu_flag_get(RCU_FLAG_FWDGTRST)) {	/* check if the system has resumed from FWDGT reset */
		rcu_all_reset_flag_clear();					/* clear the FWDGT reset flag */
	}
  /************JIANZHOU ZHANG 20241204******************************/
  ReadParaFromFlash();
  /************JIANZHOU ZHANG END***********************************/
	INS_Init();
  ReadParaFromFlash();  // Init inertial navigation device,

	g_LEDIndicatorState = LED_STATE_WHEEL_ERR_INIT_OK;
	mDelaymS( 100 );
	strcpy(versionbuf, "Hello INS622-2A...   v1.0.0\r\n");
	uart4sendmsg(versionbuf, strlen(versionbuf));
	uart4sendmsg(versionbuf, strlen(versionbuf));
	uart6sendmsg(versionbuf, strlen(versionbuf));
	uart6sendmsg(versionbuf, strlen(versionbuf));
	combineData.imuSelect =1 ; //0:mems 1:ifog//*********光纤Z轴****
	combineData.memsType = 1;//0: imu460 1:scha63x 2:adis16465 3:epsonG3xx
	combineData.Param.HP = 1;
	//***算法之前初始化*******Guolong Zhang*****
	extern _NAV_Data_Full_t  NAV_Data_Full;
	NAV_Data_Full.ins_buffer_full_flag = RETURN_FAIL;
	NAV_Data_Full.ZUPT_flag = RETURN_FAIL;
	NAV_Data_Full.acc_gyr_Cnt = 0;
	NAV_Data_Full.Nav_Status = E_NAV_STATUS_START;
	NAV_Data_Full.EARTH.gn[2] = -G0;
	NAV_Data_Full.ins_buffer_full_cnt = 0;
	NAV_Data_Full.Modacc = 0.0;
	NAV_Data_Full.Modgyr = 0.0;
	NAV_Data_Full.Modacc_std2 = 0.0;
	NAV_Data_Full.Modgyr_std2 = 0.0;
	NAV_Data_Full.Macc[0] = 0.0;
	NAV_Data_Full.Macc[1] = 0.0;
	NAV_Data_Full.Macc[2] = 0.0;
	NAV_Data_Full.Mgyr[0]=0.0;
	NAV_Data_Full.Mgyr[1]=0.0;
	NAV_Data_Full.Mgyr[2]=0.0;
	NAV_Data_Full.KF_CheckCnt = 0;
	NAV_Data_Full.Subkf2_Cnt = 0;
 NAV_Data_Full.ZUPTyaw_ST_Cnt = 0;

	//********转台数据标定的数值***跑车测试时只需一次标定即可写入flash*****编号：20250528****
	//FOG50: 5号（SN:202303090001）：1.002349136   6号（SN:202303090008）： 1.003379875
 //FOG60: 4号（SN:202402010004）：1.002215466		17号（SN:202402010003）：1.002177689			10号（SN:202402010001）：0.99339657
 //FOG60: 6号（SN:202303090001）：1.00197572	
	NAV_Data_Full.SubKF.fog_kz = 1.0016142;

	NAV_Data_Full.SubKF.Bias_gz = 0.0;//*******默认初值********
	NAV_Data_Full.SubKF.Fog_Cnt = 0;//****subkf-fog用到*****
	NAV_Data_Full.SubKF.Checkfog_flag= RETURN_FAIL;//*******起始阶段状态置0******
	NAV_Data_Full.SINS.Init_flag = RETURN_FAIL;//每次启动时默认为0
	NAV_Data_Full.Pre_att_flag = RETURN_FAIL;//每次启动置0,尚未进入组合状态
	NAV_Data_Full.GPSlastnum=0;
//*************
	while(1)
	{
		if(fpga_syn)
		 {
						fpga_syn = 0;
						get_fpgadata();
						wheel_is_running();
				if(//(abs(combineData.gnssInfo.gpssecond-combineData.gnssInfo.gpssecond982)<10000)&&//********注释掉该行，保证水平姿态能够连续输出*********
								//	(combineData.gnssInfo.gpssecond982 != 0)&&
									(combineData.gnssInfo.gpssecond != 0)
					  )
			  {
				  NAV_function();
			  }
					//if(NAV_Data_Full.Nav_Status > E_NAV_STATUS_SINS_KF_INITIAL)//******初始化完成以后再输出数据******
						{
									NAV_Output();
						}
		}
		if (gbilldebuguart4) 	uart4sendmsg_canout(&gCanRxBuf);
		LEDIndicator(g_LEDIndicatorState);
		analysisRxdata();

		if(g_Uart6_Rx_Finish==1)
		{
			g_Uart6_Rx_Finish=0;
			UartDataHandle();//串口升级句柄
		}
	}
}



//void wheel_is_running(void)
//{
//	static uint32_t lastCanCounter = 0;
//	static uint8_t canPeriod = 0;
//	if(lastCanCounter == combineData.canInfo.counter) {
//		canPeriod++;
//		if(canPeriod > 100) {
//			canPeriod = 0;
//			combineData.canInfo.flag = 0;
//		}
//	}
//	else {
//		canPeriod = 0;
//		combineData.canInfo.flag = 1;
//	}
//	lastCanCounter = combineData.canInfo.counter;
//}

#endif

